import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-simple-toast',
  templateUrl: './simple-toast.component.html',
  styleUrls: ['./simple-toast.component.scss'],
})
export class SimpleToastComponent {
  @Input() message = '';
  @Input() duration = 3000;
  @Output() closed = new EventEmitter<void>();

  ngOnInit() {
    setTimeout(() => this.closed.emit(), this.duration);
  }
}
