package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.repository.AliasRepository;
import com.wishforthecure.forconversations.repository.search.AliasSearchRepository;
import com.wishforthecure.forconversations.service.AliasService;
import com.wishforthecure.forconversations.service.dto.AliasDTO;
import com.wishforthecure.forconversations.service.mapper.AliasMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing {@link com.wishforthecure.forconversations.domain.Alias}.
 */
@Service
public class AliasServiceImpl implements AliasService {

    private static final Logger LOG = LoggerFactory.getLogger(AliasServiceImpl.class);

    private final AliasRepository aliasRepository;

    private final AliasMapper aliasMapper;

    private final AliasSearchRepository aliasSearchRepository;

    public AliasServiceImpl(AliasRepository aliasRepository, AliasMapper aliasMapper, AliasSearchRepository aliasSearchRepository) {
        this.aliasRepository = aliasRepository;
        this.aliasMapper = aliasMapper;
        this.aliasSearchRepository = aliasSearchRepository;
    }

    @Override
    public Mono<AliasDTO> save(AliasDTO aliasDTO) {
        LOG.debug("Request to save Alias : {}", aliasDTO);
        return aliasRepository.save(aliasMapper.toEntity(aliasDTO)).flatMap(aliasSearchRepository::save).map(aliasMapper::toDto);
    }

    @Override
    public Mono<AliasDTO> update(AliasDTO aliasDTO) {
        LOG.debug("Request to update Alias : {}", aliasDTO);
        return aliasRepository.save(aliasMapper.toEntity(aliasDTO)).flatMap(aliasSearchRepository::save).map(aliasMapper::toDto);
    }

    @Override
    public Mono<AliasDTO> partialUpdate(AliasDTO aliasDTO) {
        LOG.debug("Request to partially update Alias : {}", aliasDTO);

        return aliasRepository
            .findById(aliasDTO.getId())
            .map(existingAlias -> {
                aliasMapper.partialUpdate(existingAlias, aliasDTO);

                return existingAlias;
            })
            .flatMap(aliasRepository::save)
            .flatMap(savedAlias -> {
                aliasSearchRepository.save(savedAlias);
                return Mono.just(savedAlias);
            })
            .map(aliasMapper::toDto);
    }

    @Override
    public Flux<AliasDTO> findAll() {
        LOG.debug("Request to get all Aliases");
        return aliasRepository.findAll().map(aliasMapper::toDto);
    }

    public Mono<Long> countAll() {
        return aliasRepository.count();
    }

    public Mono<Long> searchCount() {
        return aliasSearchRepository.count();
    }

    @Override
    public Mono<AliasDTO> findOne(String id) {
        LOG.debug("Request to get Alias : {}", id);
        return aliasRepository.findById(id).map(aliasMapper::toDto);
    }

    @Override
    public Mono<Void> delete(String id) {
        LOG.debug("Request to delete Alias : {}", id);
        return aliasRepository.deleteById(id).then(aliasSearchRepository.deleteById(id));
    }

    @Override
    public Flux<AliasDTO> search(String query) {
        LOG.debug("Request to search Aliases for query {}", query);
        try {
            return aliasSearchRepository.search(query).map(aliasMapper::toDto);
        } catch (RuntimeException e) {
            throw e;
        }
    }
}
