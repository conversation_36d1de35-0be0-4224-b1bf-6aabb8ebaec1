package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.WhatsappMessage;
import com.wishforthecure.forconversations.domain.WhatsappMessageSource;
import com.wishforthecure.forconversations.service.dto.WhatsappMessageDTO;
import com.wishforthecure.forconversations.service.dto.WhatsappMessageSourceDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link WhatsappMessage} and its DTO {@link WhatsappMessageDTO}.
 */
@Mapper(componentModel = "spring")
public interface WhatsappMessageMapper extends EntityMapper<WhatsappMessageDTO, WhatsappMessage> {
    @Mapping(target = "whatsappMessageSource", source = "whatsappMessageSource", qualifiedByName = "whatsappMessageSourceId")
    WhatsappMessageDTO toDto(WhatsappMessage s);

    @Named("whatsappMessageSourceId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    WhatsappMessageSourceDTO toDtoWhatsappMessageSourceId(WhatsappMessageSource whatsappMessageSource);
}
