{"name": "forconversations", "version": "0.0.1-SNAPSHOT", "private": true, "description": "Description for Forconversations", "license": "UNLICENSED", "scripts": {"app:start": "./mvnw -ntp --batch-mode", "app:up": "docker compose -f src/main/docker/app.yml up --wait", "backend:build-cache": "./mvnw dependency:go-offline -ntp", "backend:debug": "./mvnw -Dspring-boot.run.jvmArguments=\"-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000\"", "backend:doc:test": "./mvnw -ntp javadoc:javadoc --batch-mode", "backend:info": "./mvnw --version", "backend:nohttp:test": "./mvnw -ntp checkstyle:check --batch-mode", "backend:start": "./mvnw -Dskip.installnodenpm -Dskip.npm -ntp --batch-mode", "backend:unit:test": "./mvnw -ntp -Dskip.installnodenpm -Dskip.npm verify --batch-mode -Dlogging.level.ROOT=OFF -Dlogging.level.tech.jhipster=OFF -Dlogging.level.com.wishforthecure.forconversations=OFF -Dlogging.level.org.springframework=OFF -Dlogging.level.org.springframework.web=OFF -Dlogging.level.org.springframework.security=OFF", "build": "npm run webapp:prod --", "build-watch": "concurrently 'npm run webapp:build:dev -- --watch' npm:backend:start", "ci:backend:test": "npm run backend:info && npm run backend:doc:test && npm run backend:nohttp:test && npm run backend:unit:test -- -P$npm_package_config_default_environment", "ci:e2e:package": "npm run java:$npm_package_config_packaging:$npm_package_config_default_environment -- -Pe2e -Denforcer.skip=true", "ci:e2e:prepare": "npm run ci:e2e:prepare:docker", "ci:e2e:prepare:docker": "npm run services:up --if-present && docker ps -a", "preci:e2e:server:start": "npm run services:db:await --if-present && npm run services:others:await --if-present", "ci:e2e:server:start": "java -jar target/e2e.$npm_package_config_packaging --spring.profiles.active=e2e,$npm_package_config_default_environment -Dlogging.level.ROOT=OFF -Dlogging.level.tech.jhipster=OFF -Dlogging.level.com.wishforthecure.forconversations=OFF -Dlogging.level.org.springframework=OFF -Dlogging.level.org.springframework.web=OFF -Dlogging.level.org.springframework.security=OFF --logging.level.org.springframework.web=ERROR", "ci:e2e:teardown": "npm run ci:e2e:teardown:docker --if-present", "ci:e2e:teardown:docker": "docker compose -f src/main/docker/services.yml down -v && docker ps -a", "ci:frontend:build": "npm run webapp:build:$npm_package_config_default_environment", "ci:frontend:test": "npm run ci:frontend:build && npm test", "ci:server:await": "echo \"Waiting for server at port $npm_package_config_backend_port to start\" && wait-on -t 180000 http-get://127.0.0.1:$npm_package_config_backend_port/management/health && echo \"Server at port $npm_package_config_backend_port started\"", "clean-www": "rimraf target/classes/static/", "cleanup": "rimraf target/", "docker:db:down": "docker compose -f src/main/docker/mongodb.yml down -v", "docker:db:up": "docker compose -f src/main/docker/mongodb.yml up --wait", "docker:elasticsearch:down": "docker compose -f src/main/docker/elasticsearch.yml down -v", "docker:elasticsearch:up": "docker compose -f src/main/docker/elasticsearch.yml up --wait", "java:docker": "./mvnw -ntp verify -DskipTests -Pprod jib:dockerBuild", "java:docker:arm64": "npm run java:docker -- -Djib-maven-plugin.architecture=arm64", "java:docker:dev": "npm run java:docker -- -Pdev,webapp", "java:docker:prod": "npm run java:docker -- -P<PERSON>rod", "java:jar": "./mvnw -ntp verify -DskipTests --batch-mode", "java:jar:dev": "npm run java:jar -- -Pdev,webapp", "java:jar:prod": "npm run java:jar -- -P<PERSON>rod", "java:war": "./mvnw -ntp verify -DskipTests --batch-mode -Pwar", "java:war:dev": "npm run java:war -- -Pdev,webapp", "java:war:prod": "npm run java:war -- -P<PERSON>rod", "jest": "jest --coverage --logHeapUsage --maxWorkers=2 --config jest.conf.js", "lint": "eslint .", "lint:fix": "npm run lint -- --fix", "prepare": "husky", "prettier:check": "prettier --check \"{,src/**/,webpack/,.blueprint/**/}*.{md,json,yml,js,cjs,mjs,ts,cts,mts,java,html,css,scss}\"", "prettier:format": "prettier --write \"{,src/**/,webpack/,.blueprint/**/}*.{md,json,yml,js,cjs,mjs,ts,cts,mts,java,html,css,scss}\"", "serve": "npm run start --", "services:up": "docker compose -f src/main/docker/services.yml up --wait", "start": "ng serve --hmr", "start-tls": "npm run webapp:dev-ssl", "pretest": "npm run lint", "test": "ng test --coverage --log-heap-usage -w=2", "test:watch": "npm run test -- --watch", "watch": "concurrently npm:start npm:backend:start", "webapp:build": "npm run clean-www && npm run webapp:build:dev", "webapp:build:dev": "ng build --configuration development", "webapp:build:prod": "ng build --configuration production", "webapp:dev": "ng serve", "webapp:dev-ssl": "ng serve --ssl", "webapp:dev-verbose": "ng serve --verbose", "webapp:prod": "npm run clean-www && npm run webapp:build:prod", "webapp:test": "npm run test --"}, "config": {"backend_port": 8080, "default_environment": "prod", "packaging": "jar"}, "overrides": {"browser-sync": "3.0.4", "webpack": "5.99.7"}, "dependencies": {"@angular/common": "19.2.9", "@angular/compiler": "19.2.9", "@angular/core": "19.2.9", "@angular/forms": "19.2.9", "@angular/localize": "19.2.9", "@angular/platform-browser": "19.2.9", "@angular/platform-browser-dynamic": "19.2.9", "@angular/router": "19.2.9", "@fortawesome/angular-fontawesome": "1.0.0", "@fortawesome/fontawesome-svg-core": "6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "6.7.2", "@ng-bootstrap/ng-bootstrap": "18.0.0", "@ngx-translate/core": "16.0.4", "@ngx-translate/http-loader": "16.0.1", "@popperjs/core": "2.11.8", "bootstrap": "5.3.6", "bootswatch": "5.3.5", "dayjs": "1.11.13", "ngx-infinite-scroll": "19.0.0", "rxjs": "7.8.2", "tslib": "2.8.1", "zone.js": "0.15.0"}, "devDependencies": {"@angular-builders/custom-webpack": "19.0.1", "@angular-builders/jest": "19.0.1", "@angular-devkit/build-angular": "19.2.10", "@angular/cli": "19.2.10", "@angular/compiler-cli": "19.2.9", "@angular/service-worker": "19.2.9", "@eslint/js": "9.26.0", "@types/jest": "29.5.14", "@types/node": "20.11.25", "angular-eslint": "19.3.0", "browser-sync": "3.0.4", "browser-sync-webpack-plugin": "2.3.0", "buffer": "6.0.3", "concurrently": "9.1.2", "copy-webpack-plugin": "13.0.0", "eslint": "9.26.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.4.0", "folder-hash": "4.1.1", "generator-jhipster": "8.11.0", "globals": "16.0.0", "husky": "9.1.7", "jest": "29.7.0", "jest-date-mock": "1.0.10", "jest-environment-jsdom": "29.7.0", "jest-junit": "16.0.0", "jest-preset-angular": "14.5.5", "jest-sonar": "0.2.16", "lint-staged": "15.5.2", "merge-jsons-webpack-plugin": "2.0.1", "prettier": "3.5.3", "prettier-plugin-java": "2.6.7", "prettier-plugin-packagejson": "2.5.11", "rimraf": "5.0.8", "swagger-ui-dist": "5.21.0", "ts-jest": "29.3.2", "typescript": "5.8.3", "typescript-eslint": "8.32.0", "wait-on": "8.0.3", "webpack-bundle-analyzer": "4.10.2", "webpack-merge": "6.0.1", "webpack-notifier": "1.15.0"}, "engines": {"node": ">=22.15.0"}, "cacheDirectories": ["node_modules"]}