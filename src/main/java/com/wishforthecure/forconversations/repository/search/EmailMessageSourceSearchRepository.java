package com.wishforthecure.forconversations.repository.search;

import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import com.wishforthecure.forconversations.domain.EmailMessageSource;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository;
import reactor.core.publisher.Flux;

/**
 * Spring Data Elasticsearch repository for the {@link EmailMessageSource} entity.
 */
public interface EmailMessageSourceSearchRepository
    extends ReactiveElasticsearchRepository<EmailMessageSource, String>, EmailMessageSourceSearchRepositoryInternal {}

interface EmailMessageSourceSearchRepositoryInternal {
    Flux<EmailMessageSource> search(String query);

    Flux<EmailMessageSource> search(Query query);
}

class EmailMessageSourceSearchRepositoryInternalImpl implements EmailMessageSourceSearchRepositoryInternal {

    private final ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    EmailMessageSourceSearchRepositoryInternalImpl(ReactiveElasticsearchTemplate reactiveElasticsearchTemplate) {
        this.reactiveElasticsearchTemplate = reactiveElasticsearchTemplate;
    }

    @Override
    public Flux<EmailMessageSource> search(String query) {
        NativeQuery nativeQuery = new NativeQuery(QueryStringQuery.of(qs -> qs.query(query))._toQuery());
        return search(nativeQuery);
    }

    @Override
    public Flux<EmailMessageSource> search(Query query) {
        return reactiveElasticsearchTemplate.search(query, EmailMessageSource.class).map(SearchHit::getContent);
    }
}
