package com.wishforthecure.forconversations.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * A EmailMessageSource.
 */
@Document(collection = "email_message_source")
@org.springframework.data.elasticsearch.annotations.Document(indexName = "emailmessagesource")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class EmailMessageSource implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private String id;

    @NotNull(message = "must not be null")
    @Field("time")
    private Instant time;

    @Field("file")
    private byte[] file;

    @NotNull
    @Field("file_content_type")
    private String fileContentType;

    @Field("messages")
    @JsonIgnoreProperties(value = { "emailMessageSource" }, allowSetters = true)
    private Set<EmailMessage> messages = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public String getId() {
        return this.id;
    }

    public EmailMessageSource id(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return this.time;
    }

    public EmailMessageSource time(Instant time) {
        this.setTime(time);
        return this;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public byte[] getFile() {
        return this.file;
    }

    public EmailMessageSource file(byte[] file) {
        this.setFile(file);
        return this;
    }

    public void setFile(byte[] file) {
        this.file = file;
    }

    public String getFileContentType() {
        return this.fileContentType;
    }

    public EmailMessageSource fileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
        return this;
    }

    public void setFileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
    }

    public Set<EmailMessage> getMessages() {
        return this.messages;
    }

    public void setMessages(Set<EmailMessage> emailMessages) {
        if (this.messages != null) {
            this.messages.forEach(i -> i.setEmailMessageSource(null));
        }
        if (emailMessages != null) {
            emailMessages.forEach(i -> i.setEmailMessageSource(this));
        }
        this.messages = emailMessages;
    }

    public EmailMessageSource messages(Set<EmailMessage> emailMessages) {
        this.setMessages(emailMessages);
        return this;
    }

    public EmailMessageSource addMessages(EmailMessage emailMessage) {
        this.messages.add(emailMessage);
        emailMessage.setEmailMessageSource(this);
        return this;
    }

    public EmailMessageSource removeMessages(EmailMessage emailMessage) {
        this.messages.remove(emailMessage);
        emailMessage.setEmailMessageSource(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmailMessageSource)) {
            return false;
        }
        return getId() != null && getId().equals(((EmailMessageSource) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "EmailMessageSource{" +
            "id=" + getId() +
            ", time='" + getTime() + "'" +
            ", file='" + getFile() + "'" +
            ", fileContentType='" + getFileContentType() + "'" +
            "}";
    }
}
