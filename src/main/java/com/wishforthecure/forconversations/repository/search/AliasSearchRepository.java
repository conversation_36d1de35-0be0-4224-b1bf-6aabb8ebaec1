package com.wishforthecure.forconversations.repository.search;

import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import com.wishforthecure.forconversations.domain.Alias;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository;
import reactor.core.publisher.Flux;

/**
 * Spring Data Elasticsearch repository for the {@link Alias} entity.
 */
public interface AliasSearchRepository extends ReactiveElasticsearchRepository<Alias, String>, AliasSearchRepositoryInternal {}

interface AliasSearchRepositoryInternal {
    Flux<Alias> search(String query);

    Flux<Alias> search(Query query);
}

class AliasSearchRepositoryInternalImpl implements AliasSearchRepositoryInternal {

    private final ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    AliasSearchRepositoryInternalImpl(ReactiveElasticsearchTemplate reactiveElasticsearchTemplate) {
        this.reactiveElasticsearchTemplate = reactiveElasticsearchTemplate;
    }

    @Override
    public Flux<Alias> search(String query) {
        NativeQuery nativeQuery = new NativeQuery(QueryStringQuery.of(qs -> qs.query(query))._toQuery());
        return search(nativeQuery);
    }

    @Override
    public Flux<Alias> search(Query query) {
        return reactiveElasticsearchTemplate.search(query, Alias.class).map(SearchHit::getContent);
    }
}
