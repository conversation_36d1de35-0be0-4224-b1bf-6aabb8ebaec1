package com.wishforthecure.forconversations.repository.search;

import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import com.wishforthecure.forconversations.domain.AudioMessageSource;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository;
import reactor.core.publisher.Flux;

/**
 * Spring Data Elasticsearch repository for the {@link AudioMessageSource} entity.
 */
public interface AudioMessageSourceSearchRepository
    extends ReactiveElasticsearchRepository<AudioMessageSource, String>, AudioMessageSourceSearchRepositoryInternal {}

interface AudioMessageSourceSearchRepositoryInternal {
    Flux<AudioMessageSource> search(String query);

    Flux<AudioMessageSource> search(Query query);
}

class AudioMessageSourceSearchRepositoryInternalImpl implements AudioMessageSourceSearchRepositoryInternal {

    private final ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    AudioMessageSourceSearchRepositoryInternalImpl(ReactiveElasticsearchTemplate reactiveElasticsearchTemplate) {
        this.reactiveElasticsearchTemplate = reactiveElasticsearchTemplate;
    }

    @Override
    public Flux<AudioMessageSource> search(String query) {
        NativeQuery nativeQuery = new NativeQuery(QueryStringQuery.of(qs -> qs.query(query))._toQuery());
        return search(nativeQuery);
    }

    @Override
    public Flux<AudioMessageSource> search(Query query) {
        return reactiveElasticsearchTemplate.search(query, AudioMessageSource.class).map(SearchHit::getContent);
    }
}
