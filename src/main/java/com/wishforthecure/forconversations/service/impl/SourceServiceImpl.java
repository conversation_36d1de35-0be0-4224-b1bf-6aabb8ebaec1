package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.repository.SourceRepository;
import com.wishforthecure.forconversations.repository.search.SourceSearchRepository;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.SourceService;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import com.wishforthecure.forconversations.service.mapper.SourceMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing {@link com.wishforthecure.forconversations.domain.Source}.
 */
@Service
public class SourceServiceImpl implements SourceService {

    private static final Logger LOG = LoggerFactory.getLogger(SourceServiceImpl.class);

    private final SourceRepository sourceRepository;

    private final SourceMapper sourceMapper;

    private final SourceSearchRepository sourceSearchRepository;

    private final MessageService messageService;

    public SourceServiceImpl(
        SourceRepository sourceRepository,
        SourceMapper sourceMapper,
        SourceSearchRepository sourceSearchRepository,
        MessageService messageService
    ) {
        this.sourceRepository = sourceRepository;
        this.sourceMapper = sourceMapper;
        this.sourceSearchRepository = sourceSearchRepository;
        this.messageService = messageService;
    }

    @Override
    public Mono<SourceDTO> save(SourceDTO sourceDTO) {
        LOG.debug("Request to save Source : {}", sourceDTO);
        return sourceRepository.save(sourceMapper.toEntity(sourceDTO)).flatMap(sourceSearchRepository::save).map(sourceMapper::toDto);
    }

    @Override
    public Mono<SourceDTO> update(SourceDTO sourceDTO) {
        LOG.debug("Request to update Source : {}", sourceDTO);
        return sourceRepository.save(sourceMapper.toEntity(sourceDTO)).flatMap(sourceSearchRepository::save).map(sourceMapper::toDto);
    }

    @Override
    public Mono<SourceDTO> partialUpdate(SourceDTO sourceDTO) {
        LOG.debug("Request to partially update Source : {}", sourceDTO);

        return sourceRepository
            .findById(sourceDTO.getId())
            .map(existingSource -> {
                sourceMapper.partialUpdate(existingSource, sourceDTO);

                return existingSource;
            })
            .flatMap(sourceRepository::save)
            .flatMap(savedSource -> {
                sourceSearchRepository.save(savedSource);
                return Mono.just(savedSource);
            })
            .map(sourceMapper::toDto);
    }

    @Override
    public Flux<SourceDTO> findAll() {
        LOG.debug("Request to get all Sources");
        return sourceRepository.findAll().map(sourceMapper::toDto);
    }

    public Mono<Long> countAll() {
        return sourceRepository.count();
    }

    public Mono<Long> searchCount() {
        return sourceSearchRepository.count();
    }

    @Override
    public Mono<SourceDTO> findOne(String id) {
        LOG.debug("Request to get Source : {}", id);
        return sourceRepository.findById(id).map(sourceMapper::toDto);
    }

    @Override
    public Mono<Void> delete(String id) {
        LOG.debug("Request to delete Source with messages: {}", id);

        // Primero obtenemos el source para tener la lista de mensajes
        return sourceRepository
            .findById(id)
            .flatMap(source -> {
                // Si el source tiene mensajes, los eliminamos uno por uno
                if (source.getMessagesIds() != null && !source.getMessagesIds().isEmpty()) {
                    LOG.debug("Deleting {} messages for source {}", source.getMessagesIds().size(), id);
                    return Flux.fromIterable(source.getMessagesIds())
                        .flatMap(messageId ->
                            messageService
                                .delete(messageId)
                                .onErrorResume(e -> {
                                    LOG.warn("Error deleting message {}: {}", messageId, e.getMessage());
                                    return Mono.empty();
                                })
                        )
                        .then(Mono.just(source));
                }
                LOG.debug("No messages to delete for source {}", id);
                return Mono.just(source);
            })
            // Luego eliminamos el source de MongoDB
            .flatMap(source -> sourceRepository.deleteById(id))
            // Finalmente eliminamos el source de Elasticsearch
            .then(sourceSearchRepository.deleteById(id))
            .doOnSuccess(v -> LOG.debug("Successfully deleted source and associated messages: {}", id))
            .doOnError(e -> LOG.error("Error deleting source {}: {}", id, e.getMessage()));
    }

    @Override
    public Flux<SourceDTO> search(String query) {
        LOG.debug("Request to search Sources for query {}", query);
        try {
            return sourceSearchRepository.search(query).map(sourceMapper::toDto);
        } catch (RuntimeException e) {
            throw e;
        }
    }
}
