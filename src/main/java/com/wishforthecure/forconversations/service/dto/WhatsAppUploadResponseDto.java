package com.wishforthecure.forconversations.service.dto;

import java.util.List;
import java.util.Objects;

public class WhatsAppUploadResponseDto {

    private List<WhatsAppUploadParseDTO> parsedLines;
    private WhatsappMessageSourceDTO whatsappMessageSourceDTO;
    private String namePersonOne;
    private String namePersonTwo;
    private AliasDTO aliasPersonOne;
    private AliasDTO aliasPersonTwo;

    // Constructor vacío para la deserialización JSON
    public WhatsAppUploadResponseDto() {}

    public WhatsAppUploadResponseDto(
        List<WhatsAppUploadParseDTO> parsedLines,
        WhatsappMessageSourceDTO whatsappMessageSourceDTO,
        String namePersonOne,
        String namePersonTwo
    ) {
        this.parsedLines = parsedLines;
        this.whatsappMessageSourceDTO = whatsappMessageSourceDTO;
        this.namePersonOne = namePersonOne;
        this.namePersonTwo = namePersonTwo;
    }

    public List<WhatsAppUploadParseDTO> getParsedLines() {
        return parsedLines;
    }

    public void setParsedLines(List<WhatsAppUploadParseDTO> parsedLines) {
        this.parsedLines = parsedLines;
    }

    public String getNamePersonOne() {
        return namePersonOne;
    }

    public void setNamePersonOne(String namePersonOne) {
        this.namePersonOne = namePersonOne;
    }

    public String getNamePersonTwo() {
        return namePersonTwo;
    }

    public void setNamePersonTwo(String namePersonTwo) {
        this.namePersonTwo = namePersonTwo;
    }

    public AliasDTO getAliasPersonOne() {
        return aliasPersonOne;
    }

    public void setAliasPersonOne(AliasDTO aliasPersonOne) {
        this.aliasPersonOne = aliasPersonOne;
    }

    public AliasDTO getAliasPersonTwo() {
        return aliasPersonTwo;
    }

    public void setAliasPersonTwo(AliasDTO aliasPersonTwo) {
        this.aliasPersonTwo = aliasPersonTwo;
    }

    public WhatsappMessageSourceDTO getWhatsappMessageSourceDTO() {
        return whatsappMessageSourceDTO;
    }

    public void setWhatsappMessageSourceDTO(WhatsappMessageSourceDTO whatsappMessageSourceDTO) {
        this.whatsappMessageSourceDTO = whatsappMessageSourceDTO;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WhatsAppUploadResponseDto that = (WhatsAppUploadResponseDto) o;
        return (
            Objects.equals(parsedLines, that.parsedLines) &&
            Objects.equals(namePersonOne, that.namePersonOne) &&
            Objects.equals(namePersonTwo, that.namePersonTwo) &&
            Objects.equals(aliasPersonOne, that.aliasPersonOne) &&
            Objects.equals(aliasPersonTwo, that.aliasPersonTwo)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(parsedLines, namePersonOne, namePersonTwo, aliasPersonOne, aliasPersonTwo);
    }

    @Override
    public String toString() {
        return (
            "WhatsAppUploadResponseDto{" +
            "parsedLines=" +
            parsedLines +
            ", namePersonOne=" +
            namePersonOne +
            ", namePersonTwo=" +
            namePersonTwo +
            '}'
        );
    }
}
