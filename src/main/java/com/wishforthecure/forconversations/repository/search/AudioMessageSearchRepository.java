package com.wishforthecure.forconversations.repository.search;

import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import com.wishforthecure.forconversations.domain.AudioMessage;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository;
import reactor.core.publisher.Flux;

/**
 * Spring Data Elasticsearch repository for the {@link AudioMessage} entity.
 */
public interface AudioMessageSearchRepository
    extends ReactiveElasticsearchRepository<AudioMessage, String>, AudioMessageSearchRepositoryInternal {}

interface AudioMessageSearchRepositoryInternal {
    Flux<AudioMessage> search(String query);

    Flux<AudioMessage> search(Query query);
}

class AudioMessageSearchRepositoryInternalImpl implements AudioMessageSearchRepositoryInternal {

    private final ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    AudioMessageSearchRepositoryInternalImpl(ReactiveElasticsearchTemplate reactiveElasticsearchTemplate) {
        this.reactiveElasticsearchTemplate = reactiveElasticsearchTemplate;
    }

    @Override
    public Flux<AudioMessage> search(String query) {
        NativeQuery nativeQuery = new NativeQuery(QueryStringQuery.of(qs -> qs.query(query))._toQuery());
        return search(nativeQuery);
    }

    @Override
    public Flux<AudioMessage> search(Query query) {
        return reactiveElasticsearchTemplate.search(query, AudioMessage.class).map(SearchHit::getContent);
    }
}
