package com.wishforthecure.forconversations.repository.search;

import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import com.wishforthecure.forconversations.domain.WhatsappMessageSource;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository;
import reactor.core.publisher.Flux;

/**
 * Spring Data Elasticsearch repository for the {@link WhatsappMessageSource} entity.
 */
public interface WhatsappMessageSourceSearchRepository
    extends ReactiveElasticsearchRepository<WhatsappMessageSource, String>, WhatsappMessageSourceSearchRepositoryInternal {}

interface WhatsappMessageSourceSearchRepositoryInternal {
    Flux<WhatsappMessageSource> search(String query);

    Flux<WhatsappMessageSource> search(Query query);
}

class WhatsappMessageSourceSearchRepositoryInternalImpl implements WhatsappMessageSourceSearchRepositoryInternal {

    private final ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    WhatsappMessageSourceSearchRepositoryInternalImpl(ReactiveElasticsearchTemplate reactiveElasticsearchTemplate) {
        this.reactiveElasticsearchTemplate = reactiveElasticsearchTemplate;
    }

    @Override
    public Flux<WhatsappMessageSource> search(String query) {
        NativeQuery nativeQuery = new NativeQuery(QueryStringQuery.of(qs -> qs.query(query))._toQuery());
        return search(nativeQuery);
    }

    @Override
    public Flux<WhatsappMessageSource> search(Query query) {
        return reactiveElasticsearchTemplate.search(query, WhatsappMessageSource.class).map(SearchHit::getContent);
    }
}
