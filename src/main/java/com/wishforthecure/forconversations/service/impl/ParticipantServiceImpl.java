package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.repository.ParticipantRepository;
import com.wishforthecure.forconversations.repository.search.ParticipantSearchRepository;
import com.wishforthecure.forconversations.service.ParticipantService;
import com.wishforthecure.forconversations.service.dto.ParticipantDTO;
import com.wishforthecure.forconversations.service.mapper.ParticipantMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing {@link com.wishforthecure.forconversations.domain.Participant}.
 */
@Service
public class ParticipantServiceImpl implements ParticipantService {

    private static final Logger LOG = LoggerFactory.getLogger(ParticipantServiceImpl.class);

    private final ParticipantRepository participantRepository;

    private final ParticipantMapper participantMapper;

    private final ParticipantSearchRepository participantSearchRepository;

    public ParticipantServiceImpl(
        ParticipantRepository participantRepository,
        ParticipantMapper participantMapper,
        ParticipantSearchRepository participantSearchRepository
    ) {
        this.participantRepository = participantRepository;
        this.participantMapper = participantMapper;
        this.participantSearchRepository = participantSearchRepository;
    }

    @Override
    public Mono<ParticipantDTO> save(ParticipantDTO participantDTO) {
        LOG.debug("Request to save Participant : {}", participantDTO);
        return participantRepository
            .save(participantMapper.toEntity(participantDTO))
            .flatMap(participantSearchRepository::save)
            .map(participantMapper::toDto);
    }

    @Override
    public Mono<ParticipantDTO> update(ParticipantDTO participantDTO) {
        LOG.debug("Request to update Participant : {}", participantDTO);
        return participantRepository
            .save(participantMapper.toEntity(participantDTO))
            .flatMap(participantSearchRepository::save)
            .map(participantMapper::toDto);
    }

    @Override
    public Mono<ParticipantDTO> partialUpdate(ParticipantDTO participantDTO) {
        LOG.debug("Request to partially update Participant : {}", participantDTO);

        return participantRepository
            .findById(participantDTO.getId())
            .map(existingParticipant -> {
                participantMapper.partialUpdate(existingParticipant, participantDTO);

                return existingParticipant;
            })
            .flatMap(participantRepository::save)
            .flatMap(savedParticipant -> {
                participantSearchRepository.save(savedParticipant);
                return Mono.just(savedParticipant);
            })
            .map(participantMapper::toDto);
    }

    @Override
    public Flux<ParticipantDTO> findAll() {
        LOG.debug("Request to get all Participants");
        return participantRepository.findAll().map(participantMapper::toDto);
    }

    public Mono<Long> countAll() {
        return participantRepository.count();
    }

    public Mono<Long> searchCount() {
        return participantSearchRepository.count();
    }

    @Override
    public Mono<ParticipantDTO> findOne(String id) {
        LOG.debug("Request to get Participant : {}", id);
        return participantRepository.findById(id).map(participantMapper::toDto);
    }

    @Override
    public Mono<Void> delete(String id) {
        LOG.debug("Request to delete Participant : {}", id);
        return participantRepository.deleteById(id).then(participantSearchRepository.deleteById(id));
    }

    @Override
    public Flux<ParticipantDTO> search(String query) {
        LOG.debug("Request to search Participants for query {}", query);
        try {
            return participantSearchRepository.search(query).map(participantMapper::toDto);
        } catch (RuntimeException e) {
            throw e;
        }
    }
}
