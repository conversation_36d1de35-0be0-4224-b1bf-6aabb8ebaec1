package com.wishforthecure.forconversations.service;

import com.wishforthecure.forconversations.service.dto.ParticipantDTO;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Interface for managing {@link com.wishforthecure.forconversations.domain.Participant}.
 */
public interface ParticipantService {
    /**
     * Save a participant.
     *
     * @param participantDTO the entity to save.
     * @return the persisted entity.
     */
    Mono<ParticipantDTO> save(ParticipantDTO participantDTO);

    /**
     * Updates a participant.
     *
     * @param participantDTO the entity to update.
     * @return the persisted entity.
     */
    Mono<ParticipantDTO> update(ParticipantDTO participantDTO);

    /**
     * Partially updates a participant.
     *
     * @param participantDTO the entity to update partially.
     * @return the persisted entity.
     */
    Mono<ParticipantDTO> partialUpdate(ParticipantDTO participantDTO);

    /**
     * Get all the participants.
     *
     * @return the list of entities.
     */
    Flux<ParticipantDTO> findAll();

    /**
     * Returns the number of participants available.
     * @return the number of entities in the database.
     *
     */
    Mono<Long> countAll();

    /**
     * Returns the number of participants available in search repository.
     *
     */
    Mono<Long> searchCount();

    /**
     * Get the "id" participant.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Mono<ParticipantDTO> findOne(String id);

    /**
     * Delete the "id" participant.
     *
     * @param id the id of the entity.
     * @return a Mono to signal the deletion
     */
    Mono<Void> delete(String id);

    /**
     * Search for the participant corresponding to the query.
     *
     * @param query the query of the search.
     * @return the list of entities.
     */
    Flux<ParticipantDTO> search(String query);
}
