package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.service.EmailMessageSourceService;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.SourceService;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing
 * {@link com.wishforthecure.forconversations.domain.EmailMessageSource}.
 */
@Service
public class EmailMessageSourceServiceImpl implements EmailMessageSourceService {

    private static final Logger LOG = LoggerFactory.getLogger(EmailMessageSourceServiceImpl.class);

    private final MessageService messageService;
    private final SourceService sourceService;

    public EmailMessageSourceServiceImpl(MessageService messageService, SourceService sourceService) {
        this.messageService = messageService;
        this.sourceService = sourceService;
    }

    @Override
    public Mono<Void> loadFile(byte[] bytes) {
        List<MessageDTO> messagesDTOList = new ArrayList<>();
        // leemos csv
        // src/main/resources/sources-examples/email_example_01.csv
        try (
            InputStream inputStream = new ByteArrayInputStream(bytes);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
        ) {
            String email;
            while ((email = reader.readLine()) != null) {
                LOG.debug(email);
                MessageDTO messageDTO = new MessageDTO(
                    Instant.now(),
                    email.split(",")[0],
                    email.split(",")[1],
                    email.split(",")[2],
                    SourceType.EMAIL
                );
                messagesDTOList.add(messageDTO);
            }
        } catch (IOException e) {
            LOG.error("Error reading file", e);
            throw new RuntimeException("Failed to read file", e);
        }
        return messageService
            .saveAll(messagesDTOList)
            .flatMap(savedMessages -> {
                // Extract all message IDs
                List<String> messageIds = savedMessages.stream().map(MessageDTO::getId).collect(Collectors.toList());

                // Set required fields on sourceDTO
                SourceDTO sourceDTO = new SourceDTO(Instant.now(), messageIds, SourceType.EMAIL, bytes, null);

                // Generate sourceId and save the source
                return sourceDTO
                    .withGeneratedIds()
                    .flatMap(updatedSource -> {
                        LOG.debug("Saving source with message IDs: {}", messageIds);
                        return sourceService.save(updatedSource);
                    });
            })
            .then();
    }
}
