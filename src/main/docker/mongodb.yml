# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: forconversations
services:
  mongodb:
    image: mongo:8.0.9
    # If you want to expose these ports outside your dev PC,
    # remove the "127.0.0.1:" prefix
    ports:
      - 127.0.0.1:27017:27017
    # volumes:
    #   - ~/volumes/jhipster/forconversations/mongodb/:/data/db/
    healthcheck:
      test: ['CMD', 'echo', '''db.runCommand("ping").ok''', '|', 'mongo', 'localhost:27017/test', '--quiet']
      interval: 5s
      timeout: 5s
      retries: 10
