package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.Participant;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;

/**
 * Spring Data MongoDB reactive repository for the Participant entity.
 */
@SuppressWarnings("unused")
@Repository
public interface ParticipantRepository extends ReactiveMongoRepository<Participant, String> {}
