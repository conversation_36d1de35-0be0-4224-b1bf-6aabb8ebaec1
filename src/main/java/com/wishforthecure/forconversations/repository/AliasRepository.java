package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.Alias;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;

/**
 * Spring Data MongoDB reactive repository for the Alias entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AliasRepository extends ReactiveMongoRepository<Alias, String> {}
