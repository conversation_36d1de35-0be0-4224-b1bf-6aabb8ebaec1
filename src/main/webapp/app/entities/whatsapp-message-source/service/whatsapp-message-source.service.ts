// src/main/webapp/app/entities/whatsapp-message-source/service/whatsapp-message-source.service.ts

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { IWhatsAppConfirmUploadRequestDto } from '../model/whatsapp-confirm-upload-request.model';
import { IWhatsAppInitiateUploadResponseDto } from '../model/whatsapp-initiate-upload-response.model';

@Injectable({ providedIn: 'root' })
export class WhatsappMessageSourceService {
  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/whatsapp-message-sources');

  constructor(
    protected http: HttpClient,
    protected applicationConfigService: ApplicationConfigService,
  ) {}

  initiateUpload(file: File): Observable<IWhatsAppInitiateUploadResponseDto> {
    const formData: FormData = new FormData();
    formData.append('file', file);
    return this.http.post<IWhatsAppInitiateUploadResponseDto>(`${this.resourceUrl}/initiate-upload`, formData);
  }

  confirmUpload(request: IWhatsAppConfirmUploadRequestDto): Observable<void> {
    return this.http.put<void>(`${this.resourceUrl}/confirm-upload`, request);
  }
}
