package com.wishforthecure.forconversations.repository.search;

import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import com.wishforthecure.forconversations.domain.EmailMessage;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository;
import reactor.core.publisher.Flux;

/**
 * Spring Data Elasticsearch repository for the {@link EmailMessage} entity.
 */
public interface EmailMessageSearchRepository
    extends ReactiveElasticsearchRepository<EmailMessage, String>, EmailMessageSearchRepositoryInternal {}

interface EmailMessageSearchRepositoryInternal {
    Flux<EmailMessage> search(String query);

    Flux<EmailMessage> search(Query query);
}

class EmailMessageSearchRepositoryInternalImpl implements EmailMessageSearchRepositoryInternal {

    private final ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    EmailMessageSearchRepositoryInternalImpl(ReactiveElasticsearchTemplate reactiveElasticsearchTemplate) {
        this.reactiveElasticsearchTemplate = reactiveElasticsearchTemplate;
    }

    @Override
    public Flux<EmailMessage> search(String query) {
        NativeQuery nativeQuery = new NativeQuery(QueryStringQuery.of(qs -> qs.query(query))._toQuery());
        return search(nativeQuery);
    }

    @Override
    public Flux<EmailMessage> search(Query query) {
        return reactiveElasticsearchTemplate.search(query, EmailMessage.class).map(SearchHit::getContent);
    }
}
