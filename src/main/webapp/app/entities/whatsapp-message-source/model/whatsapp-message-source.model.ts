import dayjs from 'dayjs';

type AnyDayjs = any; // Usamos any para evitar conflictos de tipos entre versiones de dayjs

export interface IWhatsappMessageSource {
  id: string;
  name?: string | null;
  time?: AnyDayjs | Date | null;
  file?: string | null;
  fileContentType?: string | null;
  metadata?: string | null;
}

export type NewWhatsappMessageSource = Omit<IWhatsappMessageSource, 'id'> & { id: null };
