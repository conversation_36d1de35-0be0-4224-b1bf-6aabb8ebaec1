<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="alert alert-success" *ngIf="account() !== null">
        @if (account(); as accountRef) {
          <span id="home-logged-message" jhiTranslate="home.logged.message" [translateValues]="{ username: accountRef.login }">
            You are logged in as user &quot;{{ accountRef.login }}&quot;.
          </span>
        }
      </div>

      @if (account() === null) {
        <div class="alert alert-warning">
          <span jhiTranslate="global.messages.info.authenticated.prefix">If you want to </span>
          <a class="alert-link" (click)="login()" jhiTranslate="global.messages.info.authenticated.link">sign in</a>
          <span jhiTranslate="global.messages.info.authenticated.suffix">
            <br />- Administrator (login=&quot;admin&quot; and password=&quot;admin&quot;) <br />- User (login=&quot;user&quot; and
            password=&quot;user&quot;).
          </span>
        </div>

        <div class="alert alert-warning">
          <span jhiTranslate="global.messages.info.register.noaccount">You don&apos;t have an account yet?</span>&nbsp;
          <a class="alert-link" routerLink="/account/register" jhiTranslate="global.messages.info.register.link">Register a new account</a>
        </div>
      }
    </div>
  </div>

  @if (account() !== null) {
    <!-- Whatsapp Message Source Button Component -->
    <div class="row mt-4">
      <div class="col-12">
        <whatsapp-message-source-button></whatsapp-message-source-button>
      </div>
    </div>
  }
</div>
