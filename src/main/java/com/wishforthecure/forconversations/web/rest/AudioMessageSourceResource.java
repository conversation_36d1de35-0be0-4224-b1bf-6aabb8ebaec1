package com.wishforthecure.forconversations.web.rest;

import com.wishforthecure.forconversations.service.AudioMessageSourceService;
import com.wishforthecure.forconversations.service.dto.AudioMessageSourceResponseDto;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.multipart.Part;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

/**
 * REST controller for managing
 * {@link com.wishforthecure.forconversations.domain.AudioMessageSource}.
 */
@RestController
@RequestMapping("/api/audio-message-sources")
public class AudioMessageSourceResource {

    private static final Logger LOG = LoggerFactory.getLogger(AudioMessageSourceResource.class);

    private final AudioMessageSourceService audioMessageSourceService;

    public AudioMessageSourceResource(AudioMessageSourceService audioMessageSourceService) {
        this.audioMessageSourceService = audioMessageSourceService;
    }

    @PostMapping(value = "/load-data", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<ResponseEntity<AudioMessageSourceResponseDto>> loadFile(@RequestPart("file") Part filePart) {
        return filePart
            .content()
            .collectList()
            .flatMap(buffers -> {
                try {
                    // Convertir los DataBuffer a un solo byte array
                    int size = buffers.stream().mapToInt(DataBuffer::readableByteCount).sum();
                    byte[] bytes = new byte[size];
                    int offset = 0;
                    for (DataBuffer buffer : buffers) {
                        int length = buffer.readableByteCount();
                        buffer.read(bytes, offset, length);
                        DataBufferUtils.release(buffer);
                        offset += length;
                    }

                    // Procesar el archivo
                    return audioMessageSourceService
                        .loadFile(bytes)
                        .map(response -> ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(response));
                } catch (Exception e) {
                    LOG.error("Unexpected error", e);
                    return Mono.error(
                        new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error inesperado al procesar la solicitud")
                    );
                }
            });
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Mono<Void> createAudioMessageSource(@Valid @RequestBody AudioMessageSourceResponseDto audioMessageSourceResponseDto) {
        LOG.debug("REST request to save AudioMessageSource: {}", audioMessageSourceResponseDto);
        return audioMessageSourceService
            .save(audioMessageSourceResponseDto)
            .onErrorResume(e -> {
                LOG.error("Unexpected error", e);
                return Mono.error(
                    new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error inesperado al procesar la solicitud")
                );
            });
    }
}
