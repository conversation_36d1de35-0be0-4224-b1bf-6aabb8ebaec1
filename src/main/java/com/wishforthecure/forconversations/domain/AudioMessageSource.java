package com.wishforthecure.forconversations.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * A AudioMessageSource.
 */
@Document(collection = "audio_message_source")
@org.springframework.data.elasticsearch.annotations.Document(indexName = "audiomessagesource")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AudioMessageSource implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private String id;

    @NotNull(message = "must not be null")
    @Field("time")
    private Instant time;

    @Field("file")
    private byte[] file;

    @NotNull
    @Field("file_content_type")
    private String fileContentType;

    @Field("messages")
    @JsonIgnoreProperties(value = { "audioMessageSource" }, allowSetters = true)
    private Set<AudioMessage> messages = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public String getId() {
        return this.id;
    }

    public AudioMessageSource id(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return this.time;
    }

    public AudioMessageSource time(Instant time) {
        this.setTime(time);
        return this;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public byte[] getFile() {
        return this.file;
    }

    public AudioMessageSource file(byte[] file) {
        this.setFile(file);
        return this;
    }

    public void setFile(byte[] file) {
        this.file = file;
    }

    public String getFileContentType() {
        return this.fileContentType;
    }

    public AudioMessageSource fileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
        return this;
    }

    public void setFileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
    }

    public Set<AudioMessage> getMessages() {
        return this.messages;
    }

    public void setMessages(Set<AudioMessage> audioMessages) {
        if (this.messages != null) {
            this.messages.forEach(i -> i.setAudioMessageSource(null));
        }
        if (audioMessages != null) {
            audioMessages.forEach(i -> i.setAudioMessageSource(this));
        }
        this.messages = audioMessages;
    }

    public AudioMessageSource messages(Set<AudioMessage> audioMessages) {
        this.setMessages(audioMessages);
        return this;
    }

    public AudioMessageSource addMessages(AudioMessage audioMessage) {
        this.messages.add(audioMessage);
        audioMessage.setAudioMessageSource(this);
        return this;
    }

    public AudioMessageSource removeMessages(AudioMessage audioMessage) {
        this.messages.remove(audioMessage);
        audioMessage.setAudioMessageSource(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AudioMessageSource)) {
            return false;
        }
        return getId() != null && getId().equals(((AudioMessageSource) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AudioMessageSource{" +
            "id=" + getId() +
            ", time='" + getTime() + "'" +
            ", file='" + getFile() + "'" +
            ", fileContentType='" + getFileContentType() + "'" +
            "}";
    }
}
