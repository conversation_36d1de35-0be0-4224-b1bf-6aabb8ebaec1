package com.wishforthecure.forconversations.web.rest;

import com.wishforthecure.forconversations.service.WhatsappMessageSourceService;
import com.wishforthecure.forconversations.service.dto.WhatsAppConfirmUploadRequestDto;
import com.wishforthecure.forconversations.service.dto.WhatsAppInitiateUploadResponseDto;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.multipart.Part;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(path = "/api/whatsapp-message-sources", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
public class WhatsappMessageSourceResource {

    private static final Logger LOG = LoggerFactory.getLogger(WhatsappMessageSourceResource.class);
    private final WhatsappMessageSourceService whatsappMessageSourceService;

    public WhatsappMessageSourceResource(WhatsappMessageSourceService whatsappMessageSourceService) {
        this.whatsappMessageSourceService = whatsappMessageSourceService;
    }

    @PostMapping(value = "/initiate-upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<ResponseEntity<WhatsAppInitiateUploadResponseDto>> initiateUpload(@RequestPart("file") Part filePart) {
        LOG.debug("REST request to initiate chat data upload");
        return DataBufferUtils.join(filePart.content())
            .map(dataBuffer -> {
                byte[] bytes = new byte[dataBuffer.readableByteCount()];
                dataBuffer.read(bytes);
                DataBufferUtils.release(dataBuffer);
                return bytes;
            })
            .flatMap(whatsappMessageSourceService::initiateUpload)
            .map(ResponseEntity::ok)
            .onErrorResume(e -> {
                LOG.error("Error initiating upload", e);
                return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
            });
    }

    @PutMapping(value = "/confirm-upload", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    public Mono<Void> confirmUpload(@Valid @RequestBody WhatsAppConfirmUploadRequestDto confirmRequest) {
        LOG.debug("REST request to confirm and process chat data for uploadId: {}", confirmRequest.getUploadId());
        return whatsappMessageSourceService
            .confirmAndProcessUpload(confirmRequest)
            .onErrorResume(ResponseStatusException.class, Mono::error)
            .onErrorResume(e -> {
                LOG.error("Unexpected error during upload confirmation", e);
                return Mono.error(new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Unexpected processing error."));
            });
    }
}
