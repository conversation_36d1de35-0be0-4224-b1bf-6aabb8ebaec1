import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';

import { IEmailMessageSource } from '../email-message-source.model';
import { sampleWithFullData, sampleWithNewData, sampleWithPartialData, sampleWithRequiredData } from '../email-message-source.test-samples';

import { EmailMessageSourceService, RestEmailMessageSource } from './email-message-source.service';

const requireRestSample: RestEmailMessageSource = {
  ...sampleWithRequiredData,
  time: sampleWithRequiredData.time?.toJSON(),
};

describe('EmailMessageSource Service', () => {
  let service: EmailMessageSourceService;
  let httpMock: HttpTestingController;
  let expectedResult: IEmailMessageSource | IEmailMessageSource[] | boolean | null;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    expectedResult = null;
    service = TestBed.inject(EmailMessageSourceService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  describe('Service methods', () => {
    it('should find an element', () => {
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.find('ABC').subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should create a EmailMessageSource', () => {
      const emailMessageSource = { ...sampleWithNewData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.create(emailMessageSource).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'POST' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should update a EmailMessageSource', () => {
      const emailMessageSource = { ...sampleWithRequiredData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.update(emailMessageSource).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PUT' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should partial update a EmailMessageSource', () => {
      const patchObject = { ...sampleWithPartialData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.partialUpdate(patchObject).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PATCH' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should return a list of EmailMessageSource', () => {
      const returnedFromService = { ...requireRestSample };

      const expected = { ...sampleWithRequiredData };

      service.query().subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush([returnedFromService]);
      httpMock.verify();
      expect(expectedResult).toMatchObject([expected]);
    });

    it('should delete a EmailMessageSource', () => {
      const expected = true;

      service.delete('ABC').subscribe(resp => (expectedResult = resp.ok));

      const req = httpMock.expectOne({ method: 'DELETE' });
      req.flush({ status: 200 });
      expect(expectedResult).toBe(expected);
    });

    it('should handle exceptions for searching a EmailMessageSource', () => {
      const queryObject: any = {
        page: 0,
        size: 20,
        query: '',
        sort: [],
      };
      service.search(queryObject).subscribe(() => expectedResult);

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(null, { status: 500, statusText: 'Internal Server Error' });
      expect(expectedResult).toBe(null);
    });

    describe('addEmailMessageSourceToCollectionIfMissing', () => {
      it('should add a EmailMessageSource to an empty array', () => {
        const emailMessageSource: IEmailMessageSource = sampleWithRequiredData;
        expectedResult = service.addEmailMessageSourceToCollectionIfMissing([], emailMessageSource);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(emailMessageSource);
      });

      it('should not add a EmailMessageSource to an array that contains it', () => {
        const emailMessageSource: IEmailMessageSource = sampleWithRequiredData;
        const emailMessageSourceCollection: IEmailMessageSource[] = [
          {
            ...emailMessageSource,
          },
          sampleWithPartialData,
        ];
        expectedResult = service.addEmailMessageSourceToCollectionIfMissing(emailMessageSourceCollection, emailMessageSource);
        expect(expectedResult).toHaveLength(2);
      });

      it("should add a EmailMessageSource to an array that doesn't contain it", () => {
        const emailMessageSource: IEmailMessageSource = sampleWithRequiredData;
        const emailMessageSourceCollection: IEmailMessageSource[] = [sampleWithPartialData];
        expectedResult = service.addEmailMessageSourceToCollectionIfMissing(emailMessageSourceCollection, emailMessageSource);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(emailMessageSource);
      });

      it('should add only unique EmailMessageSource to an array', () => {
        const emailMessageSourceArray: IEmailMessageSource[] = [sampleWithRequiredData, sampleWithPartialData, sampleWithFullData];
        const emailMessageSourceCollection: IEmailMessageSource[] = [sampleWithRequiredData];
        expectedResult = service.addEmailMessageSourceToCollectionIfMissing(emailMessageSourceCollection, ...emailMessageSourceArray);
        expect(expectedResult).toHaveLength(3);
      });

      it('should accept varargs', () => {
        const emailMessageSource: IEmailMessageSource = sampleWithRequiredData;
        const emailMessageSource2: IEmailMessageSource = sampleWithPartialData;
        expectedResult = service.addEmailMessageSourceToCollectionIfMissing([], emailMessageSource, emailMessageSource2);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(emailMessageSource);
        expect(expectedResult).toContain(emailMessageSource2);
      });

      it('should accept null and undefined values', () => {
        const emailMessageSource: IEmailMessageSource = sampleWithRequiredData;
        expectedResult = service.addEmailMessageSourceToCollectionIfMissing([], null, emailMessageSource, undefined);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(emailMessageSource);
      });

      it('should return initial array if no EmailMessageSource is added', () => {
        const emailMessageSourceCollection: IEmailMessageSource[] = [sampleWithRequiredData];
        expectedResult = service.addEmailMessageSourceToCollectionIfMissing(emailMessageSourceCollection, undefined, null);
        expect(expectedResult).toEqual(emailMessageSourceCollection);
      });
    });

    describe('compareEmailMessageSource', () => {
      it('should return true if both entities are null', () => {
        const entity1 = null;
        const entity2 = null;

        const compareResult = service.compareEmailMessageSource(entity1, entity2);

        expect(compareResult).toEqual(true);
      });

      it('should return false if one entity is null', () => {
        const entity1 = { id: '2f2e649a-9e17-4630-b134-ce784325165f' };
        const entity2 = null;

        const compareResult1 = service.compareEmailMessageSource(entity1, entity2);
        const compareResult2 = service.compareEmailMessageSource(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('should return false if primaryKey differs', () => {
        const entity1 = { id: '2f2e649a-9e17-4630-b134-ce784325165f' };
        const entity2 = { id: 'c0a6f021-0b63-4e7d-8132-2c27165e7054' };

        const compareResult1 = service.compareEmailMessageSource(entity1, entity2);
        const compareResult2 = service.compareEmailMessageSource(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('should return false if primaryKey matches', () => {
        const entity1 = { id: '2f2e649a-9e17-4630-b134-ce784325165f' };
        const entity2 = { id: '2f2e649a-9e17-4630-b134-ce784325165f' };

        const compareResult1 = service.compareEmailMessageSource(entity1, entity2);
        const compareResult2 = service.compareEmailMessageSource(entity2, entity1);

        expect(compareResult1).toEqual(true);
        expect(compareResult2).toEqual(true);
      });
    });
  });

  afterEach(() => {
    httpMock.verify();
  });
});
