package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.repository.MessageRepository;
import com.wishforthecure.forconversations.repository.search.MessageSearchRepository;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.mapper.MessageMapper;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing
 * {@link com.wishforthecure.forconversations.domain.Message}.
 */
@Service
public class MessageServiceImpl implements MessageService {

    private static final Logger LOG = LoggerFactory.getLogger(MessageServiceImpl.class);

    private final MessageRepository messageRepository;

    private final MessageMapper messageMapper;

    private final MessageSearchRepository messageSearchRepository;

    public MessageServiceImpl(
        MessageRepository messageRepository,
        MessageMapper messageMapper,
        MessageSearchRepository messageSearchRepository
    ) {
        this.messageRepository = messageRepository;
        this.messageMapper = messageMapper;
        this.messageSearchRepository = messageSearchRepository;
    }

    @Override
    public Mono<MessageDTO> save(MessageDTO messageDTO) {
        LOG.debug("Request to save Message : {}", messageDTO);
        return messageRepository.save(messageMapper.toEntity(messageDTO)).flatMap(messageSearchRepository::save).map(messageMapper::toDto);
    }

    @Override
    public Mono<List<MessageDTO>> saveAll(List<MessageDTO> messageDTOList) {
        LOG.debug("Request to save Message : {}", messageDTOList);
        return messageRepository
            .saveAll(messageMapper.toEntity(messageDTOList))
            .collectList()
            .flatMap(entities -> messageSearchRepository.saveAll(entities).then(Mono.just(entities)))
            .map(savedEntities -> messageMapper.toDto(savedEntities));
    }

    @Override
    public Mono<MessageDTO> update(MessageDTO messageDTO) {
        LOG.debug("Request to update Message : {}", messageDTO);
        return messageRepository.save(messageMapper.toEntity(messageDTO)).flatMap(messageSearchRepository::save).map(messageMapper::toDto);
    }

    @Override
    public Mono<MessageDTO> partialUpdate(MessageDTO messageDTO) {
        LOG.debug("Request to partially update Message : {}", messageDTO);

        return messageRepository
            .findById(messageDTO.getId())
            .map(existingMessage -> {
                messageMapper.partialUpdate(existingMessage, messageDTO);

                return existingMessage;
            })
            .flatMap(messageRepository::save)
            .flatMap(savedMessage -> {
                messageSearchRepository.save(savedMessage);
                return Mono.just(savedMessage);
            })
            .map(messageMapper::toDto);
    }

    @Override
    public Flux<MessageDTO> findAll() {
        LOG.debug("Request to get all Messages");
        return messageRepository.findAll().map(messageMapper::toDto);
    }

    public Mono<Long> countAll() {
        return messageRepository.count();
    }

    public Mono<Long> searchCount() {
        return messageSearchRepository.count();
    }

    @Override
    public Mono<MessageDTO> findOne(String id) {
        LOG.debug("Request to get Message : {}", id);
        return messageRepository.findById(id).map(messageMapper::toDto);
    }

    @Override
    public Mono<Void> delete(String id) {
        LOG.debug("Request to delete Message : {}", id);
        return messageRepository.deleteById(id).then(messageSearchRepository.deleteById(id));
    }

    @Override
    public Flux<MessageDTO> search(String query) {
        LOG.debug("Request to search Messages for query {}", query);
        try {
            return messageSearchRepository.search(query).map(messageMapper::toDto);
        } catch (RuntimeException e) {
            throw e;
        }
    }
}
