# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: forconversations
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.13.4
    # volumes:
    #   - ~/volumes/jhipster/forconversations/elasticsearch/:/usr/share/elasticsearch/data/
    # If you want to expose these ports outside your dev PC,
    # remove the "127.0.0.1:" prefix
    ports:
      - 127.0.0.1:9200:9200
      - 127.0.0.1:9300:9300
    environment:
      - 'ES_JAVA_OPTS=-Xms256m -Xmx256m'
      - 'discovery.type=single-node'
      - 'xpack.security.enabled=false'
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9200/_cluster/health?wait_for_status=green&timeout=10s']
      interval: 5s
      timeout: 10s
      retries: 10
