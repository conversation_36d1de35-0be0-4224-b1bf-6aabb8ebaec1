{"generator-jhipster": {"deploymentType": "kubernetes", "appsFolders": ["kubernetes"], "directoryPath": "../", "kubernetesServiceType": "LoadBalancer", "istio": false, "ingressDomain": "forconversations.wishforthecure.com", "monitoring": "no", "clusteredDbApps": [], "serviceDiscoveryType": "consul", "dockerRepositoryName": "wishforthecure.forconversations", "dockerPushCommand": "docker push", "kubernetesNamespace": "prod", "kubernetesUseDynamicStorage": false, "kubernetesStorageClassName": ""}}