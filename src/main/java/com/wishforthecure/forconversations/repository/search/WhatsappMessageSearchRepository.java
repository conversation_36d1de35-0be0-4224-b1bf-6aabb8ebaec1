package com.wishforthecure.forconversations.repository.search;

import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import com.wishforthecure.forconversations.domain.WhatsappMessage;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository;
import reactor.core.publisher.Flux;

/**
 * Spring Data Elasticsearch repository for the {@link WhatsappMessage} entity.
 */
public interface WhatsappMessageSearchRepository
    extends ReactiveElasticsearchRepository<WhatsappMessage, String>, WhatsappMessageSearchRepositoryInternal {}

interface WhatsappMessageSearchRepositoryInternal {
    Flux<WhatsappMessage> search(String query);

    Flux<WhatsappMessage> search(Query query);
}

class WhatsappMessageSearchRepositoryInternalImpl implements WhatsappMessageSearchRepositoryInternal {

    private final ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    WhatsappMessageSearchRepositoryInternalImpl(ReactiveElasticsearchTemplate reactiveElasticsearchTemplate) {
        this.reactiveElasticsearchTemplate = reactiveElasticsearchTemplate;
    }

    @Override
    public Flux<WhatsappMessage> search(String query) {
        NativeQuery nativeQuery = new NativeQuery(QueryStringQuery.of(qs -> qs.query(query))._toQuery());
        return search(nativeQuery);
    }

    @Override
    public Flux<WhatsappMessage> search(Query query) {
        return reactiveElasticsearchTemplate.search(query, WhatsappMessage.class).map(SearchHit::getContent);
    }
}
