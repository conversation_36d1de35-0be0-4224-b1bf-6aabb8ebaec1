package com.wishforthecure.forconversations.web.rest;

import com.wishforthecure.forconversations.service.EmailMessageSourceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.codec.multipart.Part;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

/**
 * REST controller for managing email message sources.
 */
@RestController
@RequestMapping("/api/email-message-sources")
public class EmailMessageSourceResource {

    private static final Logger LOG = LoggerFactory.getLogger(EmailMessageSourceResource.class);
    private final EmailMessageSourceService emailMessageSourceService;

    public EmailMessageSourceResource(EmailMessageSourceService emailMessageSourceService) {
        this.emailMessageSourceService = emailMessageSourceService;
    }

    @PostMapping(value = "/load-data", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<Void> loadChatData(@RequestPart("file") Part filePart) {
        LOG.debug("Processing email data file");

        return filePart
            .content()
            .collectList()
            .flatMap(buffers -> {
                try {
                    // Convertir los DataBuffer a un solo byte array
                    int size = buffers
                        .stream()
                        .mapToInt(buffer -> {
                            int count = buffer.readableByteCount();
                            DataBufferUtils.release(buffer);
                            return count;
                        })
                        .sum();

                    byte[] bytes = new byte[size];
                    int offset = 0;

                    for (DataBuffer buffer : buffers) {
                        int length = buffer.readableByteCount();
                        buffer.read(bytes, offset, length);
                        offset += length;
                    }

                    // Procesar el archivo
                    return emailMessageSourceService.loadFile(bytes);
                } catch (Exception e) {
                    LOG.error("Error processing file", e);
                    return Mono.error(
                        new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error al procesar el archivo: " + e.getMessage())
                    );
                }
            })
            .onErrorResume(e -> {
                if (!(e instanceof ResponseStatusException)) {
                    LOG.error("Unexpected error processing file", e);
                    return Mono.error(
                        new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error inesperado al procesar el archivo")
                    );
                }
                return Mono.error(e);
            });
    }
}
