package com.wishforthecure.forconversations.service.dto;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.wishforthecure.forconversations.domain.Message}
 * entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class MessageDTO implements Serializable {

    private String id;

    @NotNull(message = "must not be null")
    private Instant time;

    @NotNull(message = "must not be null")
    private String sender;

    @NotNull(message = "must not be null")
    private String recipients;

    @NotNull(message = "must not be null")
    private String content;

    @NotNull(message = "must not be null")
    private SourceType type;

    // Constructor
    public MessageDTO(Instant time, String sender, String recipients, String content, SourceType type) {
        this.time = time;
        this.sender = sender;
        this.recipients = recipients;
        this.content = content;
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return time;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getRecipients() {
        return recipients;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public SourceType getType() {
        return type;
    }

    public void setType(SourceType type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MessageDTO)) {
            return false;
        }

        MessageDTO messageDTO = (MessageDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, messageDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "MessageDTO{" +
                "id='" + getId() + "'" +
                ", time='" + getTime() + "'" +
                ", sender='" + getSender() + "'" +
                ", recipients='" + getRecipients() + "'" +
                ", content='" + getContent() + "'" +
                ", type='" + getType() + "'" +
                "}";
    }
}
