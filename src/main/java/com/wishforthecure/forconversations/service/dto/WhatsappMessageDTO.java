package com.wishforthecure.forconversations.service.dto;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the
 * {@link com.wishforthecure.forconversations.domain.WhatsappMessage} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class WhatsappMessageDTO implements Serializable {

    private String id;

    @NotNull(message = "must not be null")
    private Instant time;

    @NotNull(message = "must not be null")
    private String sender;

    @NotNull(message = "must not be null")
    private String recipient;

    @NotNull(message = "must not be null")
    private String content;

    private WhatsappMessageSourceDTO whatsappMessageSource;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return time;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getRecipient() {
        return recipient;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public WhatsappMessageSourceDTO getWhatsappMessageSource() {
        return whatsappMessageSource;
    }

    public void setWhatsappMessageSource(WhatsappMessageSourceDTO whatsappMessageSource) {
        this.whatsappMessageSource = whatsappMessageSource;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof WhatsappMessageDTO)) {
            return false;
        }

        WhatsappMessageDTO whatsappMessageDTO = (WhatsappMessageDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, whatsappMessageDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "WhatsappMessageDTO{" +
                "id='" + getId() + "'" +
                ", time='" + getTime() + "'" +
                ", sender='" + getSender() + "'" +
                ", recipient='" + getRecipient() + "'" +
                ", content='" + getContent() + "'" +
                ", whatsappMessageSource=" + getWhatsappMessageSource() +
                "}";
    }
}
