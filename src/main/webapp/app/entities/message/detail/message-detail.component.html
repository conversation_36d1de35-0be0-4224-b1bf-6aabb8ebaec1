<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (message(); as messageRef) {
      <div>
        <h2 data-cy="messageDetailsHeading"><span jhiTranslate="forconversationsApp.message.detail.title">Message</span></h2>

        <hr />

        <jhi-alert-error />

        <jhi-alert />

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="global.field.id">ID</span></dt>
          <dd>
            <span>{{ messageRef.id }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.message.time">Time</span></dt>
          <dd>
            <span>{{ messageRef.time | formatMediumDatetime }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.message.sender">Sender</span></dt>
          <dd>
            <span>{{ messageRef.sender }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.message.recipients">Recipients</span></dt>
          <dd>
            <span>{{ messageRef.recipients }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.message.content">Content</span></dt>
          <dd>
            <span>{{ messageRef.content }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.message.type">Type</span></dt>
          <dd>
            <span [jhiTranslate]="'forconversationsApp.SourceType.' + (messageRef.type ?? 'null')">{{
              { null: '', WHATSAPP: 'WHATSAPP', AUDIO: 'AUDIO', EMAIL: 'EMAIL' }[messageRef.type ?? 'null']
            }}</span>
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">Back</span>
        </button>

        <button type="button" [routerLink]="['/message', messageRef.id, 'edit']" class="btn btn-primary">
          <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span jhiTranslate="entity.action.edit">Edit</span>
        </button>
      </div>
    }
  </div>
</div>
