package com.wishforthecure.forconversations.service.dto;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

public class WhatsAppInitiateUploadResponseDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private UUID uploadId;
    private String namePersonOne;
    private String namePersonTwo;

    // constructor
    public WhatsAppInitiateUploadResponseDto() {}

    public WhatsAppInitiateUploadResponseDto(UUID uploadId, String namePersonOne, String namePersonTwo) {
        this.uploadId = uploadId;
        this.namePersonOne = namePersonOne;
        this.namePersonTwo = namePersonTwo;
    }

    // getter and setter
    public UUID getUploadId() {
        return uploadId;
    }

    public void setUploadId(UUID uploadId) {
        this.uploadId = uploadId;
    }

    public String getNamePersonOne() {
        return namePersonOne;
    }

    public void setNamePersonOne(String namePersonOne) {
        this.namePersonOne = namePersonOne;
    }

    public String getNamePersonTwo() {
        return namePersonTwo;
    }

    public void setNamePersonTwo(String namePersonTwo) {
        this.namePersonTwo = namePersonTwo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WhatsAppInitiateUploadResponseDto that = (WhatsAppInitiateUploadResponseDto) o;
        return (
            Objects.equals(uploadId, that.uploadId) &&
            Objects.equals(namePersonOne, that.namePersonOne) &&
            Objects.equals(namePersonTwo, that.namePersonTwo)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(uploadId, namePersonOne, namePersonTwo);
    }

    @Override
    public String toString() {
        return (
            "InitiateUploadResponseDto{" +
            "uploadId=" +
            uploadId +
            ", namePersonOne=" +
            namePersonOne +
            ", namePersonTwo=" +
            namePersonTwo +
            '}'
        );
    }
}
