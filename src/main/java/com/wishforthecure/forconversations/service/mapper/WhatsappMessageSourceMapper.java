package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.WhatsappMessageSource;
import com.wishforthecure.forconversations.service.dto.WhatsappMessageSourceDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link WhatsappMessageSource} and its DTO {@link WhatsappMessageSourceDTO}.
 */
@Mapper(componentModel = "spring")
public interface WhatsappMessageSourceMapper extends EntityMapper<WhatsappMessageSourceDTO, WhatsappMessageSource> {}
