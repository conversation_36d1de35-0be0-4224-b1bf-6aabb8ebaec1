package com.wishforthecure.forconversations.service;

import com.wishforthecure.forconversations.service.dto.WhatsAppConfirmUploadRequestDto;
import com.wishforthecure.forconversations.service.dto.WhatsAppInitiateUploadResponseDto;
import reactor.core.publisher.Mono;

/**
 * Service Interface for managing
 * {@link com.wishforthecure.forconversations.domain.WhatsappMessageSource}.
 */
public interface WhatsappMessageSourceService {
    /**
     * Load a file.
     *
     * @param bytes the file content as a byte array.
     * @return the parsed data.
     */
    public Mono<WhatsAppInitiateUploadResponseDto> initiateUpload(byte[] bytes);

    /**
     * Save a whatsappMessageSource.
     *
     * @param whatsAppUploadResponseDto the upload response DTO containing data to
     *                                  save.
     * @return a Mono that completes when the operation is done.
     */
    public Mono<Void> confirmAndProcessUpload(WhatsAppConfirmUploadRequestDto confirmRequest);
}
