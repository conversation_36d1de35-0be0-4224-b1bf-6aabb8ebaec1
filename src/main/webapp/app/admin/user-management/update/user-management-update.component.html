<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2 id="myUserLabel" jhiTranslate="userManagement.home.createOrEditLabel">Create or edit a user</h2>

      <jhi-alert-error />

      <div class="mb-3" [hidden]="!editForm.value.id">
        <label class="form-label" for="field_id" jhiTranslate="global.field.id">ID</label>
        <input type="text" id="field_id" class="form-control" name="id" formControlName="id" readonly />
      </div>

      <div class="mb-3">
        <label class="form-label" for="field_login" jhiTranslate="userManagement.login">Login</label>
        <input type="text" id="field_login" class="form-control" name="login" formControlName="login" />

        @let loginRef = editForm.get('login')!;
        @if (loginRef.invalid && (loginRef.dirty || loginRef.touched)) {
          <div>
            @if (loginRef?.errors?.required) {
              <small class="form-text text-danger" jhiTranslate="entity.validation.required">This field is required.</small>
            }

            @if (loginRef?.errors?.maxlength) {
              <small class="form-text text-danger" jhiTranslate="entity.validation.maxlength" [translateValues]="{ max: '50' }"
                >This field cannot be longer than 50 characters.</small
              >
            }

            @if (loginRef?.errors?.pattern) {
              <small class="form-text text-danger" jhiTranslate="entity.validation.patternLogin"
                >This field can only contain letters, digits and e-mail addresses.</small
              >
            }
          </div>
        }
      </div>

      <div class="mb-3">
        <label class="form-label" for="field_firstName" jhiTranslate="userManagement.firstName">First name</label>
        <input type="text" id="field_firstName" class="form-control" name="firstName" formControlName="firstName" />

        @let firstNameRef = editForm.get('firstName')!;
        @if (firstNameRef.invalid && (firstNameRef.dirty || firstNameRef.touched)) {
          <div>
            @if (firstNameRef?.errors?.maxlength) {
              <small class="form-text text-danger" jhiTranslate="entity.validation.maxlength" [translateValues]="{ max: '50' }"
                >This field cannot be longer than 50 characters.</small
              >
            }
          </div>
        }
      </div>

      <div class="mb-3">
        <label class="form-label" for="field_lastName" jhiTranslate="userManagement.lastName">Last name</label>
        <input type="text" id="field_lastName" class="form-control" name="lastName" formControlName="lastName" />

        @let lastNameRef = editForm.get('lastName')!;
        @if (lastNameRef.invalid && (lastNameRef.dirty || lastNameRef.touched)) {
          <div>
            @if (lastNameRef?.errors?.maxlength) {
              <small class="form-text text-danger" jhiTranslate="entity.validation.maxlength" [translateValues]="{ max: '50' }"
                >This field cannot be longer than 50 characters.</small
              >
            }
          </div>
        }
      </div>

      <div class="mb-3">
        <label class="form-label" for="field_email" jhiTranslate="userManagement.email">Email</label>
        <input type="email" id="field_email" class="form-control" name="email" formControlName="email" />

        @let emailRef = editForm.get('email')!;
        @if (emailRef.invalid && (emailRef.dirty || emailRef.touched)) {
          <div>
            @if (emailRef?.errors?.required) {
              <small class="form-text text-danger" jhiTranslate="entity.validation.required">This field is required.</small>
            }

            @if (emailRef?.errors?.maxlength) {
              <small class="form-text text-danger" jhiTranslate="entity.validation.maxlength" [translateValues]="{ max: '100' }"
                >This field cannot be longer than 100 characters.</small
              >
            }

            @if (emailRef?.errors?.minlength) {
              <small class="form-text text-danger" jhiTranslate="entity.validation.minlength" [translateValues]="{ min: '5' }"
                >This field is required to be at least 5 characters.</small
              >
            }

            @if (emailRef?.errors?.email) {
              <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.invalid">Your email is invalid.</small>
            }
          </div>
        }
      </div>

      <div class="form-check">
        <label class="form-check-label" for="activated">
          <input
            class="form-check-input"
            [attr.disabled]="editForm.value.id === undefined ? 'disabled' : null"
            type="checkbox"
            id="activated"
            name="activated"
            formControlName="activated"
          />
          <span jhiTranslate="userManagement.activated">Activated</span>
        </label>
      </div>

      @if (languages && languages.length > 0) {
        <div class="mb-3">
          <label class="form-label" for="langKey" jhiTranslate="userManagement.langKey">Language</label>
          <select class="form-control" id="langKey" name="langKey" formControlName="langKey">
            @for (language of languages; track $index) {
              <option [value]="language">{{ language | findLanguageFromKey }}</option>
            }
          </select>
        </div>
      }

      <div class="mb-3">
        <label class="form-label" for="field_authority" jhiTranslate="userManagement.profiles">Profiles</label>
        <select class="form-control" id="field_authority" multiple name="authority" formControlName="authorities">
          @for (authority of authorities(); track $index) {
            <option [value]="authority">{{ authority }}</option>
          }
        </select>
      </div>
      <button type="button" class="btn btn-secondary" (click)="previousState()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Cancel</span>
      </button>

      <button type="submit" [disabled]="editForm.invalid || isSaving()" class="btn btn-primary">
        <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Save</span>
      </button>
    </form>
  </div>
</div>
