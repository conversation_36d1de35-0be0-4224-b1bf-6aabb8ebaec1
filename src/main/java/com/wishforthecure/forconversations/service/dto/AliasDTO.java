package com.wishforthecure.forconversations.service.dto;

import com.wishforthecure.forconversations.domain.enumeration.AliasType;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.wishforthecure.forconversations.domain.Alias} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AliasDTO implements Serializable {

    private String id;

    private String value;

    private AliasType type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public AliasType getType() {
        return type;
    }

    public void setType(AliasType type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AliasDTO)) {
            return false;
        }

        AliasDTO aliasDTO = (AliasDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, aliasDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AliasDTO{" +
            "id='" + getId() + "'" +
            ", value='" + getValue() + "'" +
            ", type='" + getType() + "'" +
            "}";
    }
}
