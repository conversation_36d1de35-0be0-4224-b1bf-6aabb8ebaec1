{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"forconversations": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src/main/webapp", "prefix": "jhi", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "./webpack/webpack.custom.js"}, "outputPath": "target/classes/static/", "index": "src/main/webapp/index.html", "main": "src/main/webapp/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/main/webapp/content", "src/main/webapp/favicon.ico", "src/main/webapp/manifest.webapp", "src/main/webapp/robots.txt"], "styles": ["src/main/webapp/content/scss/vendor.scss", "src/main/webapp/content/scss/global.scss"], "scripts": []}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "serviceWorker": true, "ngswConfigPath": "ngsw-config.json", "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/main/webapp/environments/environment.ts", "with": "src/main/webapp/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "options": {"buildTarget": "forconversations:build:development", "port": 4200}, "configurations": {"production": {"buildTarget": "forconversations:build:production"}, "development": {"buildTarget": "forconversations:build:development"}}, "defaultConfiguration": "development"}, "test": {"builder": "@angular-builders/jest:run", "options": {"configPath": "jest.conf.js", "tsConfig": "tsconfig.spec.json"}}}}}, "cli": {"cache": {"enabled": true, "path": "./target/angular/", "environment": "all"}, "packageManager": "npm", "analytics": false}}