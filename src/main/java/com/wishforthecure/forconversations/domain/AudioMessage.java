package com.wishforthecure.forconversations.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * A AudioMessage.
 */
@Document(collection = "audio_message")
@org.springframework.data.elasticsearch.annotations.Document(indexName = "audiomessage")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AudioMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private String id;

    @NotNull(message = "must not be null")
    @Field("time")
    private Instant time;

    @NotNull(message = "must not be null")
    @Field("sender")
    @org.springframework.data.elasticsearch.annotations.Field(type = org.springframework.data.elasticsearch.annotations.FieldType.Text)
    private String sender;

    @NotNull(message = "must not be null")
    @Field("recipients")
    @org.springframework.data.elasticsearch.annotations.Field(type = org.springframework.data.elasticsearch.annotations.FieldType.Text)
    private String recipients;

    @NotNull(message = "must not be null")
    @Field("content")
    @org.springframework.data.elasticsearch.annotations.Field(type = org.springframework.data.elasticsearch.annotations.FieldType.Text)
    private String content;

    @NotNull(message = "must not be null")
    @Field("type")
    @org.springframework.data.elasticsearch.annotations.Field(type = org.springframework.data.elasticsearch.annotations.FieldType.Keyword)
    private SourceType type;

    @Field("audioMessageSource")
    @JsonIgnoreProperties(value = { "messages" }, allowSetters = true)
    private AudioMessageSource audioMessageSource;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public String getId() {
        return this.id;
    }

    public AudioMessage id(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return this.time;
    }

    public AudioMessage time(Instant time) {
        this.setTime(time);
        return this;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getSender() {
        return this.sender;
    }

    public AudioMessage sender(String sender) {
        this.setSender(sender);
        return this;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getRecipients() {
        return this.recipients;
    }

    public AudioMessage recipients(String recipients) {
        this.setRecipients(recipients);
        return this;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }

    public String getContent() {
        return this.content;
    }

    public AudioMessage content(String content) {
        this.setContent(content);
        return this;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public SourceType getType() {
        return this.type;
    }

    public AudioMessage type(SourceType type) {
        this.setType(type);
        return this;
    }

    public void setType(SourceType type) {
        this.type = type;
    }

    public AudioMessageSource getAudioMessageSource() {
        return this.audioMessageSource;
    }

    public void setAudioMessageSource(AudioMessageSource audioMessageSource) {
        this.audioMessageSource = audioMessageSource;
    }

    public AudioMessage audioMessageSource(AudioMessageSource audioMessageSource) {
        this.setAudioMessageSource(audioMessageSource);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AudioMessage)) {
            return false;
        }
        return getId() != null && getId().equals(((AudioMessage) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AudioMessage{" +
            "id=" + getId() +
            ", time='" + getTime() + "'" +
            ", sender='" + getSender() + "'" +
            ", recipients='" + getRecipients() + "'" +
            ", content='" + getContent() + "'" +
            ", type='" + getType() + "'" +
            "}";
    }
}
